/**
 * Fix the email index issue in the database
 * This script removes the unique index on the email field that's causing registration issues
 * Run with: node src/scripts/fix-email-index.js
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  envLines.forEach(line => {
    const equalIndex = line.indexOf('=');
    if (equalIndex > 0) {
      const key = line.substring(0, equalIndex).trim();
      const value = line.substring(equalIndex + 1).trim();
      if (key && value) {
        process.env[key] = value;
      }
    }
  });
}

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';
console.log('Using MongoDB URI:', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));

async function fixEmailIndex() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    const db = mongoose.connection.db;
    const collection = db.collection('users');
    
    // List all indexes
    console.log('\nCurrent indexes on users collection:');
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, JSON.stringify(index.key));
    });
    
    // Check if email index exists
    const emailIndexExists = indexes.some(index => 
      index.key && index.key.email !== undefined
    );
    
    if (emailIndexExists) {
      console.log('\n🔧 Found email index. Attempting to drop it...');
      
      try {
        // Try to drop the email index
        await collection.dropIndex('email_1');
        console.log('✅ Successfully dropped email_1 index');
      } catch (error) {
        if (error.message.includes('index not found')) {
          console.log('ℹ️  email_1 index not found (might have different name)');
          
          // Try to find and drop any email-related indexes
          for (const index of indexes) {
            if (index.key && index.key.email !== undefined) {
              try {
                await collection.dropIndex(index.name);
                console.log(`✅ Successfully dropped ${index.name} index`);
              } catch (dropError) {
                console.log(`❌ Failed to drop ${index.name}:`, dropError.message);
              }
            }
          }
        } else {
          console.log('❌ Error dropping email index:', error.message);
        }
      }
    } else {
      console.log('\nℹ️  No email index found. Database should be working correctly.');
    }
    
    // List indexes after cleanup
    console.log('\nIndexes after cleanup:');
    const finalIndexes = await collection.indexes();
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, JSON.stringify(index.key));
    });
    
    // Test creating a user with null email to verify fix
    console.log('\n🧪 Testing user creation...');
    try {
      const testResult = await collection.insertOne({
        accessKey: 'test-key-' + Date.now(),
        name: 'Test User',
        email: null,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log('✅ Test user creation successful!');
      
      // Clean up test user
      await collection.deleteOne({ _id: testResult.insertedId });
      console.log('🧹 Test user cleaned up');
      
    } catch (testError) {
      console.log('❌ Test user creation failed:', testError.message);
      if (testError.message.includes('E11000') && testError.message.includes('email')) {
        console.log('⚠️  Email index issue still exists. Manual intervention may be required.');
      }
    }
    
    console.log('\n✅ Database fix completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Run the function
fixEmailIndex();
