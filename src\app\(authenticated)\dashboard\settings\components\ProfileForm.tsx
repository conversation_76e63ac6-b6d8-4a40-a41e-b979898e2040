"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { profileSchema, ProfileFormValues } from '../schemas';
import { Copy, RefreshCw, Loader2, Eye, AlertTriangle } from 'lucide-react';

interface ProfileFormProps {
  initialData: {
    accessKey: string;
    name?: string;
  };
  onProfileUpdate: () => void;
}

export function ProfileForm({ initialData, onProfileUpdate }: ProfileFormProps) {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [previewKey, setPreviewKey] = useState<string>('');
  const [showRegenerateDialog, setShowRegenerateDialog] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: initialData.name || '',
    },
  });

  // Update form when initialData changes
  useEffect(() => {
    form.reset({
      name: initialData.name || '',
    });
  }, [initialData.name, form]);

  const copyToClipboard = async (key?: string) => {
    try {
      const keyToCopy = key || initialData.accessKey;
      await navigator.clipboard.writeText(keyToCopy);
      toast.success('Access key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const generateKeyPreview = async () => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'generate'
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (response.ok) {
        setPreviewKey(data.accessKey);
        setShowRegenerateDialog(true);
      } else {
        toast.error('Failed to generate key preview');
      }
    } catch (error) {
      toast.error('Failed to generate key preview');
    }
  };

  const saveProfile = async (values: ProfileFormValues) => {
    setIsSaving(true);

    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to update profile';
        throw new Error(errorMessage);
      }

      // Refresh profile data
      onProfileUpdate();

      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const confirmRegenerateKey = async () => {
    setIsRegenerating(true);
    setShowRegenerateDialog(false);

    try {
      const response = await fetch('/api/auth/regenerate-key', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to regenerate access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      onProfileUpdate();

      toast.success('Access key regenerated successfully! Please save your new key.');
    } catch (error: any) {
      console.error('Key regeneration error:', error);
      toast.error(error.message || 'Failed to regenerate access key');
    } finally {
      setIsRegenerating(false);
      setPreviewKey('');
    }
  };

  return (
    <div className="space-y-4">
      {/* Profile Information Form */}
      <Card className="shadow-sm border-l-4 border-l-green-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Profile Information
          </CardTitle>
          <CardDescription>
            Manage your profile details and account settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(saveProfile)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-semibold">Display Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your name (optional)"
                        {...field}
                        maxLength={50}
                        className="focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                      <span className="text-green-500">ℹ</span>
                      This name will be displayed in your navbar and profile
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSaving}
                className="w-full bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Profile'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Access Key Management */}
      <Card className="shadow-sm border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            Access Key
          </CardTitle>
          <CardDescription>
            Your unique access key for authentication
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-semibold text-blue-900 dark:text-blue-100">Your Access Key</label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard()}
                    className="h-8 px-3 border-blue-300 hover:bg-blue-100 dark:border-blue-700 dark:hover:bg-blue-900"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                  <Dialog open={showRegenerateDialog} onOpenChange={setShowRegenerateDialog}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={generateKeyPreview}
                        disabled={isRegenerating}
                        className="h-8 px-3 border-orange-300 hover:bg-orange-100 dark:border-orange-700 dark:hover:bg-orange-900"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        Preview New Key
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <AlertTriangle className="h-5 w-5 text-orange-500" />
                          Regenerate Access Key
                        </DialogTitle>
                        <DialogDescription>
                          This will replace your current access key. Make sure to save the new key safely.
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Current Key:</label>
                          <div className="font-mono text-sm p-2 bg-muted rounded border mt-1">
                            {initialData.accessKey}
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-green-600 dark:text-green-400">New Key Preview:</label>
                          <div className="font-mono text-sm p-2 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800 mt-1 font-semibold">
                            {previewKey}
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(previewKey)}
                            className="mt-2 h-7 px-2 text-xs"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy New Key
                          </Button>
                        </div>
                      </div>

                      <DialogFooter className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowRegenerateDialog(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={confirmRegenerateKey}
                          disabled={isRegenerating}
                          className="bg-orange-600 hover:bg-orange-700"
                        >
                          {isRegenerating ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Regenerating...
                            </>
                          ) : (
                            'Confirm Regenerate'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <div className="font-mono text-lg font-bold p-4 bg-white dark:bg-gray-900 rounded-lg border-2 border-blue-300 dark:border-blue-700 text-center shadow-sm">
                {initialData.accessKey}
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300 mt-3 flex items-start gap-2">
                <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>This is your unique access key. Keep it safe and don't share it with anyone.</span>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground space-y-3">
                <div className="flex items-start gap-2">
                  <div className="text-lg">🔐</div>
                  <div>
                    <p className="font-semibold text-foreground mb-2">Privacy-focused account</p>
                    <ul className="space-y-1.5 text-xs">
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-0.5">•</span>
                        <span>No personal information stored beyond what you choose to add</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-blue-500 mt-0.5">•</span>
                        <span>Access key is your primary identifier</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-orange-500 mt-0.5">•</span>
                        <span>Preview new keys before regenerating for security</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-purple-500 mt-0.5">•</span>
                        <span>New keys are immediately saved to your account</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
